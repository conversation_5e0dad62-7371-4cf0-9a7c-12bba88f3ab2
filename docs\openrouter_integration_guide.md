# OpenRouter Llama模型集成指南

## 概述

本指南介绍如何在AI对冲基金系统中使用OpenRouter API作为GROQ API的备用选择来调用Llama模型。当GROQ API配额用完时，系统可以自动切换到OpenRouter继续使用相同的Llama模型。

## 功能特性

- ✅ **备用API支持**: 当GROQ API配额用完时，可使用OpenRouter作为备用
- ✅ **免费模型**: 支持OpenRouter的免费Llama模型（`meta-llama/llama-4-scout:free`和`meta-llama/llama-4-maverick:free`）
- ✅ **确定性行为**: 支持temperature=0.0和seed=42的确定性配置
- ✅ **无缝切换**: 在UI中可以选择不同的提供商
- ✅ **完全兼容**: 与现有的LLM调用接口完全兼容
- ✅ **零成本**: 使用免费模型，无需担心API费用

## 配置步骤

### 1. 获取OpenRouter API密钥

1. 访问 [OpenRouter官网](https://openrouter.ai/)
2. 注册账户并登录
3. 在控制台中创建API密钥
4. 复制您的API密钥（格式：`sk-or-v1-...`）

### 2. 配置环境变量

在项目根目录的`.env`文件中添加OpenRouter API密钥：

```bash
# OpenRouter API配置
OPENROUTER_API_KEY=sk-or-v1-your-api-key-here
```

### 3. 验证配置

运行测试脚本验证配置是否正确：

```bash
# 测试单个免费模型
python test_api/test_openrouter_llama.py

# 对比两个免费模型
python test_api/test_openrouter_free_models.py
```

如果配置正确，您应该看到：
```
🎉 所有测试通过！OpenRouter Llama集成成功！
```

## 使用方法

### 在主程序中使用

当运行主程序时，您现在可以在模型选择界面中看到OpenRouter选项：

```bash
python src/main.py
```

在模型选择界面中，您会看到：
- `[groq] llama-4-scout-17b` - 使用GROQ API
- `[openrouter] llama-4-scout (free)` - 使用OpenRouter免费API
- `[openrouter] llama-4-maverick (free)` - 使用OpenRouter免费API

### 在回测中使用

当运行回测时，同样可以选择OpenRouter模型：

```bash
python src/backtester.py
```

### 程序化使用

在代码中直接使用OpenRouter模型：

```python
from src.llm.models import get_model, ModelProvider

# 使用OpenRouter免费Llama模型
llm = get_model(
    model_name="meta-llama/llama-4-scout:free",  # 或 "meta-llama/llama-4-maverick:free"
    model_provider=ModelProvider.OPENROUTER
)

# 调用模型
response = llm.invoke("您的提示词")
```

## API配额管理

### GROQ vs OpenRouter

| 提供商 | 免费配额 | 付费选项 | 延迟 | 可靠性 | 免费模型 |
|--------|----------|----------|------|--------|----------|
| GROQ | 有限 | 是 | 低 (1.5秒) | 高 | 否 |
| OpenRouter | 免费模型可用 | 是 | 中等 (3.5秒) | 高 | 是 |

### 建议使用策略

1. **主要使用GROQ**: 优先使用GROQ API，因为它通常有更低的延迟（约1.5秒 vs 3.5秒）
2. **备用使用OpenRouter免费模型**: 当GROQ配额用完时切换到OpenRouter免费模型
3. **免费模型选择**:
   - `llama-4-scout:free`: 响应稍慢但更详细
   - `llama-4-maverick:free`: 响应更快，适合快速查询
4. **监控使用量**: 定期检查API的使用情况，免费模型无需担心成本

## 技术实现细节

### 模型提供商枚举

```python
class ModelProvider(str, Enum):
    GROQ = "Groq"
    OPENROUTER = "OpenRouter"
    # ... 其他提供商
```

### OpenRouter配置

OpenRouter使用OpenAI兼容的API接口，配置如下：

```python
ChatOpenAI(
    model=model_name,
    api_key=openrouter_api_key,
    base_url="https://openrouter.ai/api/v1",
    temperature=0.0,
    seed=42
)
```

### 确定性行为

两个提供商都支持确定性配置：
- `temperature=0.0`: 确保输出的一致性
- `seed=42`: 固定随机种子

## 故障排除

### 常见问题

1. **API密钥错误**
   ```
   OPENROUTER_API_KEY环境变量未设置
   ```
   **解决方案**: 检查`.env`文件中的API密钥配置

2. **网络连接问题**
   ```
   Connection timeout
   ```
   **解决方案**: 检查网络连接和防火墙设置

3. **模型不可用**
   ```
   Model not found
   ```
   **解决方案**: 确认OpenRouter支持所选模型

### 调试步骤

1. 运行测试脚本：`python test_api/test_openrouter_llama.py`
2. 检查API密钥格式和有效性
3. 验证网络连接
4. 查看OpenRouter控制台的使用情况

## 成本优化

### 使用建议

1. **优先级策略**: 先用免费/便宜的API，再用付费API
2. **缓存结果**: 对相同的输入缓存LLM响应
3. **批量处理**: 尽可能批量处理请求以减少API调用次数

### 监控工具

建议使用以下方式监控API使用：
- OpenRouter控制台的使用统计
- 应用程序日志中的API调用记录
- 成本跟踪和预算设置

## 更新日志

### v1.0.0 (2024-06-17)
- ✅ 添加OpenRouter API支持
- ✅ 支持meta-llama/llama-4-scout-17b-16e-instruct模型
- ✅ 确定性行为配置
- ✅ 完整的测试套件
- ✅ 前端UI集成

## 支持

如果您在使用过程中遇到问题，请：

1. 首先运行测试脚本进行诊断
2. 检查本指南的故障排除部分
3. 查看OpenRouter官方文档
4. 联系技术支持团队

---

**注意**: 请妥善保管您的API密钥，不要将其提交到版本控制系统中。
